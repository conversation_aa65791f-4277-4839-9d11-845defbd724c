import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Link as LinkIcon, ExternalLink } from 'lucide-react';
import { Candidate } from '@/types/candidate';
import { createExternalLinkProps } from '@/utils/urlUtils';

interface CandidateInfoTabsProps {
  candidate: Candidate;
  activeTab: string;
  onTabChange: (value: string) => void;
}

/**
 * Helper function to display field values with N/A for empty values
 */
const displayValue = (value: any, type: 'text' | 'number' | 'date' | 'array' = 'text'): string => {
  if (value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0)) {
    return 'N/A';
  }

  switch (type) {
    case 'number':
      // Handle both numeric values and numeric strings from PostgreSQL
      if (typeof value === 'number') {
        return value.toString();
      }
      if (typeof value === 'string' && !isNaN(Number(value)) && value.trim() !== '') {
        return Number(value).toString();
      }
      return 'N/A';
    case 'date':
      return value ? new Date(value).toLocaleDateString() : 'N/A';
    case 'array':
      return Array.isArray(value) && value.length > 0 ? value.join(', ') : 'N/A';
    default:
      return value.toString();
  }
};

/**
 * Helper component for field display
 */
const FieldDisplay = ({ label, value, type = 'text' }: { label: string; value: any; type?: 'text' | 'number' | 'date' | 'array' }) => {
  const displayedValue = displayValue(value, type);
  const isEmpty = displayedValue === 'N/A';

  return (
    <div className="space-y-1">
      <label className="text-sm font-medium text-muted-foreground">{label}</label>
      <p className={`text-sm ${isEmpty ? 'text-muted-foreground italic' : ''}`}>
        {displayedValue}
      </p>
    </div>
  );
};

/**
 * CandidateInfoTabs component displays detailed candidate information
 * organized in tabbed sections for better navigation and readability.
 */
export default function CandidateInfoTabs({ candidate, activeTab, onTabChange }: CandidateInfoTabsProps) {
  return (
    <Tabs value={activeTab} onValueChange={onTabChange}>
      <TabsList className="mb-4 grid w-full grid-cols-6">
        <TabsTrigger value="basic">Basic Info</TabsTrigger>
        <TabsTrigger value="professional">Professional</TabsTrigger>
        <TabsTrigger value="social">Social & Portfolio</TabsTrigger>
        <TabsTrigger value="assessment">Assessment</TabsTrigger>
        <TabsTrigger value="process">Process</TabsTrigger>
        <TabsTrigger value="intake">Intake Responses</TabsTrigger>
      </TabsList>

      <TabsContent value="basic">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>Core candidate details and contact information</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <FieldDisplay label="First Name" value={candidate.firstName} />
              <FieldDisplay label="Last Name" value={candidate.lastName} />
              <FieldDisplay label="Email" value={candidate.email} />
              <FieldDisplay label="Phone" value={candidate.phone} />
              <FieldDisplay label="Location" value={candidate.location} />
              <FieldDisplay label="Stargety ID" value={candidate.stargetyId} />
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FieldDisplay label="Applied Date" value={candidate.appliedDate} />
              <FieldDisplay label="Created At" value={candidate.createdAt} type="date" />
              <FieldDisplay label="Updated At" value={candidate.updatedAt} type="date" />
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="professional">
        <Card>
          <CardHeader>
            <CardTitle>Professional Information</CardTitle>
            <CardDescription>Work experience, education, and career details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FieldDisplay label="Current Position" value={candidate.position} />
              <FieldDisplay label="Current Company" value={candidate.currentCompany} />
              <FieldDisplay label="Years of Experience" value={candidate.experienceYears} type="number" />
              <FieldDisplay label="Education" value={candidate.education} />
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FieldDisplay label="Desired Salary" value={candidate.desiredSalary ? `${candidate.salaryCurrency} ${candidate.desiredSalary}` : null} />
              <FieldDisplay label="Availability Date" value={candidate.availabilityDate} type="date" />
              <FieldDisplay label="Source" value={candidate.source} />
              <FieldDisplay label="English Level" value={candidate.englishLevel} />
            </div>

            <Separator />

            <div>
              <FieldDisplay label="Skills" value={candidate.skills} type="array" />
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="social">
        <Card>
          <CardHeader>
            <CardTitle>Social Links & Portfolio</CardTitle>
            <CardDescription>Online presence and portfolio information</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <label className="text-sm font-medium text-muted-foreground">LinkedIn</label>
                {candidate.socialLinks?.linkedin ? (
                  <a {...createExternalLinkProps(candidate.socialLinks.linkedin)} className="flex items-center gap-1 text-sm text-blue-600 hover:underline">
                    <LinkIcon className="h-4 w-4" />
                    LinkedIn Profile
                  </a>
                ) : (
                  <p className="text-sm text-muted-foreground italic">N/A</p>
                )}
              </div>

              <div className="space-y-1">
                <label className="text-sm font-medium text-muted-foreground">GitHub</label>
                {candidate.socialLinks?.github ? (
                  <a {...createExternalLinkProps(candidate.socialLinks.github)} className="flex items-center gap-1 text-sm text-blue-600 hover:underline">
                    <LinkIcon className="h-4 w-4" />
                    GitHub Profile
                  </a>
                ) : (
                  <p className="text-sm text-muted-foreground italic">N/A</p>
                )}
              </div>

              <div className="space-y-1">
                <label className="text-sm font-medium text-muted-foreground">Twitter</label>
                {candidate.socialLinks?.twitter ? (
                  <a {...createExternalLinkProps(candidate.socialLinks.twitter)} className="flex items-center gap-1 text-sm text-blue-600 hover:underline">
                    <LinkIcon className="h-4 w-4" />
                    Twitter Profile
                  </a>
                ) : (
                  <p className="text-sm text-muted-foreground italic">N/A</p>
                )}
              </div>

              <div className="space-y-1">
                <label className="text-sm font-medium text-muted-foreground">Portfolio</label>
                {candidate.portfolio ? (
                  <a {...createExternalLinkProps(candidate.portfolio)} className="flex items-center gap-1 text-sm text-blue-600 hover:underline">
                    <ExternalLink className="h-4 w-4" />
                    Portfolio Website
                  </a>
                ) : (
                  <p className="text-sm text-muted-foreground italic">N/A</p>
                )}
              </div>
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <label className="text-sm font-medium text-muted-foreground">Resume</label>
                {candidate.resume ? (
                  <a {...createExternalLinkProps(candidate.resume)} className="flex items-center gap-1 text-sm text-blue-600 hover:underline">
                    <ExternalLink className="h-4 w-4" />
                    View Resume
                  </a>
                ) : (
                  <p className="text-sm text-muted-foreground italic">N/A</p>
                )}
              </div>

              <div className="space-y-1">
                <label className="text-sm font-medium text-muted-foreground">Cover Letter</label>
                {candidate.coverLetter ? (
                  <a {...createExternalLinkProps(candidate.coverLetter)} className="flex items-center gap-1 text-sm text-blue-600 hover:underline">
                    <ExternalLink className="h-4 w-4" />
                    View Cover Letter
                  </a>
                ) : (
                  <p className="text-sm text-muted-foreground italic">N/A</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="assessment">
        <Card>
          <CardHeader>
            <CardTitle>Assessment & Interview</CardTitle>
            <CardDescription>Interview scores, assessments, and evaluation details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FieldDisplay label="Interview Score" value={candidate.interviewScore} type="number" />
              <FieldDisplay label="Drive Score" value={candidate.driveScore} type="number" />
              <FieldDisplay label="Resilience Score" value={candidate.resilienceScore} type="number" />
              <FieldDisplay label="Collaboration Score" value={candidate.collaborationScore} type="number" />
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FieldDisplay label="Challenge" value={candidate.challenge} />
              <FieldDisplay label="Result" value={candidate.result} />
            </div>

            <Separator />

            <div className="space-y-4">
              <FieldDisplay label="Interview Notes" value={candidate.interviewNotes} />
              <FieldDisplay label="Challenge Notes" value={candidate.challengeNotes} />
              <FieldDisplay label="Challenge Feedback" value={candidate.challengeFeedback} />
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="process">
        <Card>
          <CardHeader>
            <CardTitle>Process Information</CardTitle>
            <CardDescription>Application process and status details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FieldDisplay label="Duplicate Status" value={candidate.isDuplicate} />
              <FieldDisplay label="In Talent Incubator" value={candidate.isInIncubator ? 'Yes' : 'No'} />
            </div>

            <Separator />

            <div>
              <FieldDisplay label="Notes" value={candidate.notes} />
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="intake">
        <Card>
          <CardHeader>
            <CardTitle>Intake Responses</CardTitle>
            <CardDescription>Responses from the initial candidate intake form</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {candidate.intakeResponses && candidate.intakeResponses.length > 0 ? (
                candidate.intakeResponses.map((response, index) => (
                  <div key={index} className="border-l-4 border-primary/20 pl-4">
                    <h4 className="font-medium text-sm">{response.question}</h4>
                    <p className="text-sm text-muted-foreground mt-1">{response.answer}</p>
                  </div>
                ))
              ) : (
                <p className="text-sm text-muted-foreground italic">No intake responses available</p>
              )}
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
}
