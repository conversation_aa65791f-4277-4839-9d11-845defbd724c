import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ArrowLeft, Calendar, Edit, Sprout, User } from 'lucide-react';
import { Candidate } from '@/types/candidate';
import { getPrimaryStatusLabel, getSecondaryStatusLabel, PrimaryStatus } from '@/lib/constants/candidateStatus';
import CandidateForm from '@/components/candidates/CandidateForm';

interface CandidateHeaderProps {
  candidate: Candidate;
  isEditDialogOpen: boolean;
  isSaving: boolean;
  onEditCandidate: () => void;
  onAddToIncubator: () => void;
  onUpdateCandidate: (data: any) => Promise<void>;
  onCloseEditDialog: () => void;
}

/**
 * CandidateHeader component displays the candidate's basic information,
 * status, and action buttons at the top of the candidate details page.
 */
export default function CandidateHeader({
  candidate,
  isEditDialogOpen,
  isSaving,
  onEditCandidate,
  onAddToIncubator,
  onUpdateCandidate,
  onCloseEditDialog,
}: CandidateHeaderProps) {
  return (
    <div className="mb-6">
      <Link to="/candidates">
        <Button variant="ghost" className="gap-2 mb-4">
          <ArrowLeft className="h-4 w-4" />
          Back to Candidates
        </Button>
      </Link>

      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center gap-4">
          <div className="relative">
            <div className={`h-16 w-16 rounded-full bg-secondary flex items-center justify-center overflow-hidden ${
              candidate.isInIncubator ? 'ring-4 ring-amber-400 ring-offset-2' : ''
            }`}>
              {candidate.imageUrl ? (
                <img src={candidate.imageUrl} alt={candidate.name} className="h-full w-full object-cover" />
              ) : (
                <User className="h-8 w-8 text-muted-foreground" />
              )}
            </div>
            {candidate.isInIncubator && (
              <div className="absolute -bottom-1 -right-1 h-6 w-6 bg-amber-400 rounded-full flex items-center justify-center border-2 border-white">
                <Sprout className="h-5 w-5 text-amber-600" />
              </div>
            )}
          </div>
          <div>
            <h1 className="text-2xl font-bold">{candidate.name}</h1>
            <p className="text-muted-foreground">
              {candidate.position ? candidate.position : <span className="italic text-muted-foregound">Position not specified</span>}
            </p>
            <div className="flex items-center gap-3 mt-1 text-sm">
              <span className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                Applied {candidate.appliedDate}
              </span>
              <div className="flex flex-col">
                <Badge variant="outline" className="capitalize mb-1">
                  {getPrimaryStatusLabel(candidate.status as PrimaryStatus)}
                </Badge>
                {candidate.secondaryStatus && (
                  <span className="text-xs text-muted-foreground">
                    {getSecondaryStatusLabel(candidate.secondaryStatus)}
                  </span>
                )}
              </div>
              {candidate.location && (
                <span className="flex items-center gap-1">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  {candidate.location}
                </span>
              )}
            </div>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button onClick={onEditCandidate} className="gap-2" variant="default">
            <Edit className="h-4 w-4" />
            Edit Candidate
          </Button>

          {!candidate.isInIncubator && (
            <Button onClick={onAddToIncubator} variant="secondary">
              Add to Talent Incubator
            </Button>
          )}
        </div>

        {/* Edit Candidate Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={onCloseEditDialog}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Candidate: {candidate.name}</DialogTitle>
              <DialogDescription>
                Update candidate information using the form below.
              </DialogDescription>
            </DialogHeader>

            <CandidateForm
              candidate={candidate}
              onSubmit={onUpdateCandidate}
              isLoading={isSaving}
            />
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
